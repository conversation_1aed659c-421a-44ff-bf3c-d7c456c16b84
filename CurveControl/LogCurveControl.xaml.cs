﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Media;
using Avalonia.Controls.Shapes;

namespace TartanLogApp.CurveControl
{
    /// <summary>
    /// LogCurveControl.xaml 的交互逻辑
    /// </summary>
    public partial class LogCurveControl : UserControl
    {
        public LogCurveControl()
        {
            InitializeComponent();
            HeaderArea.Children.Clear();
            CurveArea.Children.Clear();
            _Series = new List<Series>();
        }
        private List<Series> _Series;
        public List<Series> Series
        {
            get => _Series;
            set
            {
                _Series = value;
                //UpdateChart();
            }
        }

        public double MinDepth { get; set; }
        public double MaxDepth { get; set; }

        public double XScaleMin { get; set; }
        public double XScaleMax { get; set; }

        public double YScaleMin { get; set; }
        public double YScaleMax { get; set; }

        public double XGridInterval { get; set; }
        public double YGridInterval => YMajorGridInterval / 5;

        public double XMajorGridInterval { get; set; }
        public double YMajorGridInterval { get; set; }

        private IBrush GridStroke = new SolidColorBrush(Color.FromRgb(51, 153, 102));
        private double GridThickness = 1;

        private int cntYStep;
        private double YfirstVal;

        private bool ChartUpdate = false;

        //public static readonly DependencyProperty StrokeProperty = DependencyProperty.Register(
        //    "GridStroke", typeof(Brush), typeof(Series),
        //    new PropertyMetadata(default(Brush)));

        //public Brush GridStroke
        //{
        //    get { return (Brush)GetValue(StrokeProperty); }
        //    set { SetValue(StrokeProperty, value); }
        //}

        ///// <summary>
        ///// The stroke thickness property
        ///// </summary>
        //public static readonly DependencyProperty StrokeThicknessProperty = DependencyProperty.Register(
        //    "GridThickness", typeof(double), typeof(Series),
        //    new PropertyMetadata(default(double)));
        ///// <summary>
        ///// Gets or sets the series stroke thickness.
        ///// </summary>
        //public double GridThickness
        //{
        //    get { return (double)GetValue(StrokeThicknessProperty); }
        //    set { SetValue(StrokeThicknessProperty, value); }
        //}

        public void ClearCanvas()
        {            
            ChartUpdate = false;
            HeaderArea.Children.Clear();
            CurveArea.Children.Clear();
            RulerArea.Children.Clear();            
        }

        public void UpdateChart()
        {
            DataInit();
            ChartUpdate = true;
            HeaderArea.Children.Clear();
            CurveArea.Children.Clear();
            RulerArea.Children.Clear();
            DrawHeader();
            DrawGrid();
            DrawCurve();

        }

        public void Refresh()
        {
            if (!ChartUpdate) return;
            HeaderArea.Children.Clear();
            CurveArea.Children.Clear();
            RulerArea.Children.Clear();
            DrawHeader();
            DrawGrid();
            DrawCurve();
        }

        private void DataInit()
        {
            /*
            MinDepth = 50000;
            MaxDepth = 0;
            foreach (var serie in Series)
            {
                double Max = serie.Values.Max(o => o.Item1);
                serie.MaxVal = serie.Values.Max(o => o.Item2);
                double Min = serie.Values.Min(o => o.Item1);
                serie.MinVal = serie.Values.Min(o => o.Item2);
                if (Min < MinDepth) MinDepth = Min;
                if (Max > MaxDepth) MaxDepth = Max;
            }
            */

            YMajorGridInterval = 20;
            MaxDepth += YMajorGridInterval;

            cntYStep = 6;
            YScaleMax = MaxDepth;
            YScaleMin = YScaleMax - cntYStep * YMajorGridInterval;

            if (YMajorGridInterval > 10)
                YfirstVal = (int)(YScaleMin / 10) * 10 + 10;
            else
                YfirstVal = ((int)(YScaleMin / YMajorGridInterval) + 1) * YMajorGridInterval;

        }

        private void DrawHeader()
        {
            HeaderArea.Children.Clear();
            int seriesCnt = Series.Count;
            double h = HeaderArea.ActualHeight;
            double w = HeaderArea.ActualWidth;
            int k = 0;
            double gridInterval = h / (seriesCnt + 1);
            foreach (var serie in Series)
            {
                k++;
                Line serieLegend = new Line();
                serieLegend.Stroke = serie.Stroke;
                serieLegend.StrokeThickness = serie.StrokeThickness;
                serieLegend.X1 = 0;
                serieLegend.X2 = w;
                serieLegend.Y1 = k * gridInterval;
                serieLegend.Y2 = k * gridInterval;

                TextBlock tbMin = new TextBlock();
                tbMin.Text = serie.MinVal.ToString("0.0");
                tbMin.Foreground = serie.Stroke;
                HeaderArea.Children.Add(tbMin);
                Canvas.SetLeft(tbMin, 0);
                Canvas.SetBottom(tbMin, (seriesCnt + 1 - k) * gridInterval);

                TextBlock tbTitle = new TextBlock();
                tbTitle.Text = serie.Title;
                tbTitle.Foreground = serie.Stroke;
                tbTitle.FontWeight = FontWeights.Bold;
                HeaderArea.Children.Add(tbTitle);
                Canvas.SetLeft(tbTitle, w / 2 - 10);
                Canvas.SetBottom(tbTitle, (seriesCnt + 1 - k) * gridInterval);

                TextBlock tbMax = new TextBlock();
                tbMax.Text = serie.MaxVal.ToString("0.0");
                tbMax.Foreground = serie.Stroke;
                HeaderArea.Children.Add(tbMax);
                Canvas.SetRight(tbMax, 0);
                Canvas.SetBottom(tbMax, (seriesCnt + 1 - k) * gridInterval);

                HeaderArea.Children.Add(serieLegend);
            }

        }

        private void DrawGrid()
        {
            double h = CurveArea.ActualHeight;
            double w = CurveArea.ActualWidth;
            XScaleMax = 100;
            XScaleMin = 0;
            XGridInterval = 10;
            double yStep = h / cntYStep;
            double xStep = w / (XScaleMax - XScaleMin) * XGridInterval;

            double YfirstPos = (YfirstVal - YScaleMin) / YMajorGridInterval * yStep;

            //绘制井深轴大间隔线
            for (int i = 0; i < cntYStep; i++)
            {
                Line xlineGrid = new Line();
                xlineGrid.Stroke = GridStroke;

                xlineGrid.StrokeThickness = 2;
                xlineGrid.X1 = 0;
                xlineGrid.X2 = w;
                xlineGrid.Y1 = i * yStep + YfirstPos;
                xlineGrid.Y2 = i * yStep + YfirstPos;

                CurveArea.Children.Add(xlineGrid);

                TextBlock textBlock = new TextBlock();
                textBlock.Text = (YMajorGridInterval * i + YfirstVal).ToString();

                Canvas.SetLeft(textBlock, 8);
                Canvas.SetTop(textBlock, i * yStep + YfirstPos - 5);
                RulerArea.Children.Add(textBlock);

            }

            //绘制井深轴小间隔线
            double yVal = YfirstVal;
            for (int i = 0; ; i++)
            {
                Line xlineGrid = new Line();
                xlineGrid.Stroke = GridStroke;
                xlineGrid.StrokeDashArray = new DoubleCollection { 5, 2 };
                xlineGrid.StrokeThickness = 1;
                xlineGrid.X1 = 0;
                xlineGrid.X2 = w;
                xlineGrid.Y1 = YfirstPos - i * yStep / YMajorGridInterval * YGridInterval;
                xlineGrid.Y2 = YfirstPos - i * yStep / YMajorGridInterval * YGridInterval;

                CurveArea.Children.Add(xlineGrid);

                yVal -= YGridInterval;
                if (yVal < YScaleMin) break;
            }
            yVal = YfirstVal;
            for (int i = 0; ; i++)
            {
                Line xlineGrid = new Line();
                xlineGrid.Stroke = GridStroke;
                xlineGrid.StrokeDashArray = new DoubleCollection { 5, 2 };
                xlineGrid.StrokeThickness = 1;
                xlineGrid.X1 = 0;
                xlineGrid.X2 = w;
                xlineGrid.Y1 = i * yStep / YMajorGridInterval * YGridInterval + YfirstPos;
                xlineGrid.Y2 = i * yStep / YMajorGridInterval * YGridInterval + YfirstPos;

                CurveArea.Children.Add(xlineGrid);

                yVal += YGridInterval;
                if (yVal > YScaleMax) break;

            }

            //绘制数值轴小间隔线
            for (int i = 1; i < (int)(w / xStep); i++)
            {
                Line ylineGrid = new Line();
                ylineGrid.Stroke = GridStroke;
                ylineGrid.StrokeDashArray = new DoubleCollection { 5, 2 };
                ylineGrid.StrokeThickness = 1;
                ylineGrid.X1 = i * xStep;
                ylineGrid.X2 = i * xStep;
                ylineGrid.Y1 = 0;
                ylineGrid.Y2 = h;

                CurveArea.Children.Add(ylineGrid);
            }

        }

        private void DrawCurve()
        {
            foreach (var serie in Series)
            {
                XScaleMax = serie.MaxVal;
                XScaleMin = serie.MinVal;

                double h = CurveArea.ActualHeight;
                double w = CurveArea.ActualWidth;

                double xStep = w / (XScaleMax - XScaleMin);
                double yStep = h / cntYStep / YMajorGridInterval;

                Path path = new Path();
                path.Stroke = serie.Stroke;
                path.StrokeThickness = serie.StrokeThickness;
                path.Fill = null;

                PathGeometry geometry = new PathGeometry();
                PathFigure f = new PathFigure();
                var x = serie.Values.FirstOrDefault(o => o.Item1 >= YScaleMin);
                if (x == null) continue;
                f.StartPoint = new Point((x.Item2 - XScaleMin) * xStep, (x.Item1 - YScaleMin) * yStep);

                var shows = serie.Values.Where(o => o.Item1 >= YScaleMin && o.Item1 <= YScaleMax);

                foreach (var item in shows)
                {
                    Point p = new Point
                    {
                        X = (item.Item2 - XScaleMin) * xStep,
                        Y = (item.Item1 - YScaleMin) * yStep
                    };
                    LineSegment segment = new LineSegment
                    {
                        Point = p
                    };
                    f.Segments.Add(segment);
                }

                geometry.Figures.Add(f);
                path.Data = geometry;

                CurveArea.Children.Add(path);

            }
        }

        private void Redraw()
        {
            CurveArea.Children.Clear();
            RulerArea.Children.Clear();
            DrawGrid();
            DrawCurve();
        }

        private void CurveArea_MouseWheel(object sender, PointerWheelEventArgs e)
        {
            if (!ChartUpdate)
                return;

            YScaleMax -= e.Delta * 0.01 * YGridInterval;
            YScaleMin -= e.Delta * 0.01 * YGridInterval;

            if (e.Delta > 0 && YScaleMin < MinDepth)
            {
                YScaleMin = MinDepth;
                YScaleMax = MinDepth + cntYStep * YMajorGridInterval;
            }
            if (e.Delta < 0 && YScaleMax > MaxDepth)
            {
                YScaleMax = MaxDepth;
                YScaleMin = YScaleMax - cntYStep * YMajorGridInterval;
            }

            if (YfirstVal < YScaleMin)
            {
                YfirstVal += YMajorGridInterval;
            }

            if (YfirstVal - YScaleMin > YMajorGridInterval)
            {
                YfirstVal -= YMajorGridInterval;
            }

            Redraw();
        }

        private void RulerArea_MouseWheel(object sender, PointerWheelEventArgs e)
        {
            if (!ChartUpdate)
                return;

            if (e.Delta > 0)
            {
                cntYStep--;
                if (cntYStep < 4)
                {
                    cntYStep = 7;
                    if (YMajorGridInterval == 5)
                    {
                        YMajorGridInterval = 4;
                    }

                    YMajorGridInterval /= 2;

                    if (YMajorGridInterval < 1)
                    {
                        YMajorGridInterval = 1;
                        cntYStep = 4;
                    }

                }
                if (YfirstVal - YScaleMin > YMajorGridInterval)
                {
                    YfirstVal -= YMajorGridInterval;
                }

            }
            else
            {
                cntYStep++;
                if (cntYStep > 7)
                {
                    cntYStep = 4;
                    YMajorGridInterval *= 2;
                    if (YMajorGridInterval == 4)
                    {
                        YMajorGridInterval = 5;
                    }
                }
                if (YScaleMin + cntYStep * YMajorGridInterval - YMajorGridInterval > MaxDepth)
                {
                    cntYStep--;
                    if (cntYStep < 4)
                    {
                        cntYStep = 7;
                        YMajorGridInterval /= 2;
                    }
                }
            }

            YScaleMax = YScaleMin + cntYStep * YMajorGridInterval;

            Redraw();
        }

        private void CurveArea_MouseRightButtonUp(object sender, PointerReleasedEventArgs e)
        {
            if (!ChartUpdate)
                return;

            string text = "";

            UIElement line = null;
            UIElement tb = null;
            UIElement tbV = null;

            foreach (UIElement item in CurveArea.Children)
            {
                if (typeof(Line) == item.GetType())
                {
                    if (((Line)item).Tag != null)
                        line = item;
                }
                if (typeof(TextBlock) == item.GetType())
                {
                    if (((TextBlock)item).Tag != null)
                        tbV = item;
                }
            }
            if (line != null)
                CurveArea.Children.Remove(line);

            if (tbV != null)
                CurveArea.Children.Remove(tbV);

            foreach (UIElement item in RulerArea.Children)
            {
                if (typeof(TextBlock) == item.GetType())
                {
                    if (((TextBlock)item).Tag != null)
                        tb = item;
                }
            }
            if (line != null)
                CurveArea.Children.Remove(line);
            if (tb != null)
                RulerArea.Children.Remove(tb);

            Point point = e.GetPosition(CurveArea);

            double h = CurveArea.ActualHeight;
            double w = CurveArea.ActualWidth;

            Line hline = new Line();
            hline.Stroke = Brush.Parse("Black");
            hline.StrokeDashArray = new DoubleCollection { 2, 2 };
            hline.StrokeThickness = 2;
            hline.X1 = 0;
            hline.X2 = w;
            hline.Y1 = point.Y;
            hline.Y2 = point.Y;
            hline.Tag = "H_Line";
            CurveArea.Children.Add(hline);

            double depth = point.Y / h * cntYStep * YMajorGridInterval + YScaleMin;

            foreach (var serie in Series)
            {
                double val = -999;
                var ss = serie.Values.FirstOrDefault((x) => x.Item1 >= depth);
                if (ss != null)
                    val = ss.Item2;
                text += serie.Title + ":" + val.ToString("0.0") + " ";
            }

            text = text.Substring(0, text.Length - 1);
            TextBlock tbValue = new TextBlock();
            tbValue.Text = text;
            tbValue.Foreground = new SolidColorBrush(Color.Parse("DarkBlue"));
            tbValue.FontSize = 12;
            tbValue.FontWeight = FontWeights.Black;
            tbValue.Tag = "Text";
            Canvas.SetRight(tbValue, 0);
            Canvas.SetTop(tbValue, point.Y - 20);

            CurveArea.Children.Add(tbValue);

            TextBlock textBlock = new TextBlock();
            textBlock.Text = depth.ToString("0.0");
            textBlock.Tag = "Text";
            Canvas.SetLeft(textBlock, 3);
            Canvas.SetTop(textBlock, point.Y - 5);

            RulerArea.Children.Add(textBlock);


        }

        private void UserControl_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            if (ChartUpdate)
            {
                CurveArea.Children.Clear();
                RulerArea.Children.Clear();
                HeaderArea.Children.Clear();
                DrawHeader();
                DrawGrid();
                DrawCurve();
            }
        }

    }
}
