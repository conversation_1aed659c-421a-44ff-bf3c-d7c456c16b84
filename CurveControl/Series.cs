using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Avalonia;
using Avalonia.Media;

namespace TartanLogApp.CurveControl
{
    public class Series : AvaloniaObject
    {
    
        public bool Reverse { get; set; }
        public bool isLogarithm { get; set; }

        /// <summary>
        /// The title property
        /// </summary>
        public static readonly StyledProperty<string> TitleProperty =
            AvaloniaProperty.Register<Series, string>(nameof(Title));
        /// <summary>
        /// Gets or sets series title
        /// </summary>
        public string Title
        {
            get { return (string)GetValue(TitleProperty); }
            set { SetValue(TitleProperty, value); }
        }

        /// <summary>
        /// The stroke property
        /// </summary>
        public static readonly StyledProperty<IBrush> StrokeProperty =
            AvaloniaProperty.Register<Series, IBrush>(nameof(Stroke));
        /// <summary>
        /// Gets or sets series stroke, if this property is null then a SolidColorBrush will be assigned according to series position in collection and Chart.Colors property
        /// </summary>
        public IBrush Stroke
        {
            get { return GetValue(StrokeProperty); }
            set { SetValue(StrokeProperty, value); }
        }

        /// <summary>
        /// The stroke thickness property
        /// </summary>
        public static readonly StyledProperty<double> StrokeThicknessProperty =
            AvaloniaProperty.Register<Series, double>(nameof(StrokeThickness));
        /// <summary>
        /// Gets or sets the series stroke thickness.
        /// </summary>
        public double StrokeThickness
        {
            get { return (double)GetValue(StrokeThicknessProperty); }
            set { SetValue(StrokeThicknessProperty, value); }
        }

        /// <summary>
        /// The Minimal Value Property
        /// </summary>
        public static readonly StyledProperty<double> MinValProperty =
            AvaloniaProperty.Register<Series, double>(nameof(MinVal));
        /// <summary>
        /// Gets or sets the Minimal Value of series.
        /// </summary>
        public double MinVal
        {
            get { return (double)GetValue(MinValProperty); }
            set { SetValue(MinValProperty, value); }
        }

        /// <summary>
        /// The Maximal Value Property
        /// </summary>
        public static readonly StyledProperty<double> MaxValProperty =
            AvaloniaProperty.Register<Series, double>(nameof(MaxVal));
        /// <summary>
        /// Gets or sets the Maximal Value of series.
        /// </summary>
        public double MaxVal
        {
            get { return (double)GetValue(MaxValProperty); }
            set { SetValue(MaxValProperty, value); }
        }
 

        public Series()
        {
            Initialize();
        }

        public Series(string title)
        {
            Initialize();
            Title = title;
        }

        public int NumPoint { get => Values.Count; }
        public List<Tuple<double, double>> Values { get;private set; }

        public void AddValue(double depth, double val)
        {
            Values.Add(new Tuple<double, double>(depth,val));
            //MaxVal = Values.Max(o => o.Item2);
            //MinVal = Values.Min(o => o.Item2);
        }

        public void Clear()
        {
            Values.Clear();           
        }

        private void Initialize()
        {
            this.Reverse = false;
            this.isLogarithm = false;
            
            Values = new List<Tuple<double, double>>();
            
        }

        public static class DefaultStroke
        {
            public static SolidColorBrush[] serieStrokes =
            {
                new SolidColorBrush(Color.Parse("#EE1111")),
                new SolidColorBrush(Color.Parse("#2D89EF")),
                new SolidColorBrush(Color.Parse("#FFC40D")),
                new SolidColorBrush(Color.Parse("#00FF80")),
                new SolidColorBrush((Color)ColorConverter.ConvertFromString("#2B5797")),
                new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FF0097"))
            };
            
        }
    }
}
