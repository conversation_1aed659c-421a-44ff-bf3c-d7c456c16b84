<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <RootNamespace>TartanLogApp</RootNamespace>
    <AssemblyName>TartanLogApp</AssemblyName>
    <UseWPF>true</UseWPF>
    <Nullable>disable</Nullable>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>

  <ItemGroup>
    <!-- Entity Framework Core for .NET 8 -->
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.0" />
    
    <!-- System.Text.Json is included in .NET 8 by default -->
    <!-- Most System.* packages are now part of .NET 8 runtime -->
  </ItemGroup>

  <!-- Resources are automatically included in SDK-style projects -->
  <ItemGroup>
    <Resource Include="ThemeResource\*.png" />
  </ItemGroup>

</Project>
