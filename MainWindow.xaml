﻿<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="using:TartanLogApp"
        xmlns:Control="using:TartanLogApp.CurveControl"
        mc:Ignorable="d" Loaded="Window_Loaded" Closing="Window_Closing"
        x:Class="TartanLogApp.MainWindow"
        Title="达坦实时Gamma成像系统" Height="800" Width="1280">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="50"/>
            <RowDefinition />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="550"/>
        </Grid.ColumnDefinitions>
        <Image x:Name="imglogo" Source="ThemeResource\tartanlogo2.png" Stretch="Uniform" HorizontalAlignment="Left" Height="30" Margin="10,0"/>
        <TextBlock FontSize="22" FontWeight="Black" VerticalAlignment="Center" HorizontalAlignment="Center">达坦实时Gamma成像系统</TextBlock>
        <GroupBox Grid.Row="1" BorderBrush="Black" Margin="20,10" Width="380" Height="180" HorizontalAlignment="Left" VerticalAlignment="Top" Header="Job 设置" FontSize="16">
            <Grid Margin="10">
                <TextBlock x:Name="tbWell" Text="HF6-5BA" FontSize="14" VerticalAlignment="Top" HorizontalAlignment="Right" Margin="20,10,10,5"/>
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Height="40">
                        <TextBlock Width="45" FontSize="16" VerticalAlignment="Center" Text="JobId"  Margin="10,5"/>
                        <TextBox x:Name="tbJobID" Width="70"  Height="30" Margin="10,0" Padding="5" FontSize="16" TextAlignment="Center" VerticalAlignment="Center" TextChanged="tbJobID_TextChanged"/>
                        <Button x:Name="btnSaveJob" Content="导出" Background="AliceBlue" Width="40" Height="30" Margin="0,0,5,0" Click="btnSaveJob_Click"/>
                        <Ellipse x:Name="lblStatus" Height="10" Fill="Red" Width="10"/>
                    </StackPanel>
                   
                    <StackPanel Orientation="Horizontal" Height="48">
                        <TextBlock Width="45" FontSize="16" VerticalAlignment="Center" Text="井  号"  Margin="10,5"/>
                        <TextBox x:Name="tbWellNm" Width="120"  Height="30" Margin="10,0" Padding="5" FontSize="16" TextAlignment="Center" VerticalAlignment="Center"/>
                        <TextBlock Width="45" FontSize="16" VerticalAlignment="Center" Text="趟  钻"  Margin="10,5"/>
                        <ComboBox x:Name="cbRuns" Width="70" Height="30" Background="AliceBlue" />
                        <!--<CheckBox x:Name="ckSurvey" Content="显示测斜" VerticalAlignment="Center" VerticalContentAlignment="Center" Margin="25,0,0,0"/>-->
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" Height="40" Margin="10,5,0,0">
                        <Button x:Name="btnJobConnect" Content="连接" Background="AliceBlue" Width="60" Height="30" Margin="10,0,0,0" Click="btnJobConnect_Click"/>
                        <Button x:Name="btnJobDisConnect" Content="断开" Background="AliceBlue" Width="60" Height="30" Margin="20,0" Click="btnJobDisConnect_Click"/>
                        <Button x:Name="btnDataTrans" Content="数据远传" IsEnabled="True" Background="AliceBlue" Width="80" Height="30" Margin="20,0,10,0" Click="btnDataTrans_Click"/>
                        <Button x:Name="btnNetListen" Content="接收" IsEnabled="True" Background="AliceBlue" Width="50" Height="30" Margin="0" Click="btnNetListen_Click"/>
                    </StackPanel>
                </StackPanel>
            </Grid>
        </GroupBox>
        <GroupBox Grid.Row="1" BorderBrush="Black" Margin="410,10,0,0" Width="300" Height="180" HorizontalAlignment="Left" VerticalAlignment="Top" Header="Gamma 设置" FontSize="16">
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="80"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Orientation="Horizontal" Height="50" Grid.ColumnSpan="2">
                    <ComboBox x:Name="cbGRs" Width="80" Height="30" Background="AliceBlue" />
                    <Button x:Name="btnAddGR" Content="添加" Background="AliceBlue" Width="60" Height="30" Margin="20,0,15,0" Click="btnAddGR_Click"/>
                    <CheckBox x:Name="ckSmooth" Content="曲线平滑" VerticalAlignment="Center" VerticalContentAlignment="Center" Click="ckSmooth_Click"/>
                </StackPanel>
                
                <TextBlock x:Name="tbGRlist" Width="190" Height="30" Padding="5" Grid.Row="1" FontSize="14" TextAlignment="Center" VerticalAlignment="Stretch" Text="GV1,GV2" Background="AliceBlue" />
               
                <StackPanel Orientation="Horizontal" Height="40" Grid.Row="2">
                    <TextBlock Width="96" FontSize="16" VerticalAlignment="Center" Text="刷新间隔 (秒)"  Margin="5"/>
                    <TextBox x:Name="tbRefresh" Width="75" Height="30" Padding="5" FontSize="16" TextAlignment="Center" VerticalAlignment="Center"/>
                </StackPanel>

                <Button x:Name="btnGRClear" Grid.Row="1" Grid.Column="1" Content="清空" Background="AliceBlue" Width="60" Height="30" Click="btnGRClear_Click"/>
                <Button x:Name="btnInterval" Grid.Row="2" Grid.Column="1"  Content="确定" Background="AliceBlue" Width="60" Height="30" Click="btnInterval_Click"/>
            </Grid>
        </GroupBox>
        <GroupBox Grid.Row="1" BorderBrush="Black" Margin="20,190,0,0" Width="690" Height="140" HorizontalAlignment="Left" VerticalAlignment="Top" Header="成像设置" FontSize="16">
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition />
                    <RowDefinition />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="300"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <StackPanel Orientation="Horizontal" Height="80" Grid.RowSpan="2">
                    <TextBlock Text="扇区" VerticalAlignment="Center" Margin="20"/>
                    <ComboBox x:Name="cbSector" Width="60" Height="25" SelectedIndex="0" SelectionChanged="cbSector_SelectionChanged">
                        <ComboBoxItem Content="0"/>
                        <ComboBoxItem Content="2"/>
                        <ComboBoxItem Content="4"/>
                    </ComboBox>
                </StackPanel>

                <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" Height="60" Grid.Column="1" Grid.RowSpan="2">
                    <TextBlock FontSize="12" Text="左" VerticalAlignment="Center" Margin="5"/>
                    <ComboBox x:Name="cbLeft" FontSize="12" Width="60" Height="25" SelectedIndex="0" />
                </StackPanel>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Height="60" Grid.Column="1" Grid.RowSpan="2">
                    <TextBlock FontSize="12" Text="右" VerticalAlignment="Center" Margin="5"/>
                    <ComboBox x:Name="cbRight" FontSize="12" Width="60" Height="25" SelectedIndex="0" />
                </StackPanel>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Height="30" Grid.Column="1">
                    <TextBlock FontSize="12" Text="上" VerticalAlignment="Top" Margin="5"/>
                    <ComboBox x:Name="cbUp" FontSize="12" Width="60" VerticalAlignment="Top" Height="25" SelectedIndex="0" />
                </StackPanel>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Height="30" Grid.Column="1" Grid.Row="1">
                    <TextBlock FontSize="12" Text="下" VerticalAlignment="Bottom" Margin="5"/>
                    <ComboBox x:Name="cbDown" FontSize="12" Width="60" VerticalAlignment="Bottom" Height="25" SelectedIndex="0" />
                </StackPanel>
                <Button x:Name="btnGammaImgOK" Content="确定" Grid.Column="2" Grid.RowSpan="2" Background="AliceBlue" Height="30" Width="60" Click="btnGammaImgOK_Click"/>
            </Grid>
        </GroupBox>

        <TextBlock Text="实时Gamma曲线" Grid.Column="1" FontSize="18" Margin="12,0,210,0" VerticalAlignment="Center" FontWeight="Bold" Foreground="#FF46BD63" />

        <StackPanel Orientation="Horizontal" Grid.Column="1" HorizontalAlignment="Right" Margin="5,0,10,5">
            <Button x:Name="btnSeriesConfig" Width="48" Height="24" BorderBrush="BurlyWood" Background="AliceBlue" FontWeight="Bold" Foreground="BurlyWood" VerticalAlignment="Bottom" Click="btnSeriesConfig_Click">设置</Button>
            <Button x:Name="btnImgExport" Width="48" Height="24" BorderBrush="DarkCyan" Background="AliceBlue" FontWeight="Bold" Foreground="DarkCyan" Margin="20,0,0,0" VerticalAlignment="Bottom" Click="btnImgExport_Click">导出</Button>
        </StackPanel>
        <Control:LogCurveImgControl x:Name="Curve" Grid.Column="1" Grid.Row="1" Margin="10,0,10,50" />
        <DataGrid x:Name="lstDataSet" Grid.Row="1" Margin="20,350,0,50" HorizontalAlignment="Left" Width="690" AutoGenerateColumns="False">
            <DataGrid.Columns>
                <DataGridTextColumn Header="趟钻" Width="40" Binding="{Binding BitRun}" />
                <DataGridTextColumn Header="测深" Width="80" Binding="{Binding MeasureDepth}" />
                <DataGridTextColumn Header="标签" Width="70" Binding="{Binding TagName}" />
                <DataGridTextColumn Header="数值" Width="75" Binding="{Binding OrignalVal}" />
                <DataGridTextColumn Header="时间" Width="140" Binding="{Binding Timestamp}" />
                <DataGridTextColumn Header="Scale" Width="60" Binding="{Binding Scale}" />
                <DataGridTextColumn Header="Offset" Width="60" Binding="{Binding Offset}" />
                <DataGridTextColumn Header="零长" Width="60" Binding="{Binding GammaToBit}" />
                <DataGridTextColumn Header="结果" Width="75" Binding="{Binding ResultVal}" />
            </DataGrid.Columns>
        </DataGrid>

    </Grid>
</Window>
